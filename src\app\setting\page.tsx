"use client";
import Button from "@/components/common/Button";
import Input from "@/components/common/Input";
import { useFetchTherapistData } from "@/context/TherapistContext";
import SettingLayout from "@/layout/dashboard/SettingLayout";
import {
  updateSettingData,
  uploadProfileImage,
} from "@/services/setting.service";
import { fetcher } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { UserCircle, File, Trash, X } from "@phosphor-icons/react";
import { useFormik } from "formik";
import Image from "next/image";
import React, { useEffect, useMemo, useState } from "react";
import { mutate } from "swr";
import * as Yup from "yup";
import { FormErrors } from "@/utils/formUtils";

interface CachedValues {
  name?: string;
  email?: string;
  phone?: string;
  address?: {
    streetAddress?: string;
    pincode?: string;
    district?: string;
    state?: string;
  };
  panCard?: string;
  gstNumber?: string;
}

interface Address {
  streetAddress?: string;
  pincode?: string;
  district?: string;
  state?: string;
}

interface TherapistData {
  name?: string;
  email?: string;
  phone?: string;
  address?: Address;
  panCard?: string;
  gstNumber?: string;
  profilePhoto?: string;
}

interface PincodeAPIResponse {
  Status: string;
  PostOffice: { District: string; State: string }[];
}

const ProfileInfo = () => {
  const [profileimage, setProfileImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cachedValues, setCachedValues] = useState<CachedValues>({});
  const [hasGst, setHasGst] = useState<null | "yes" | "no">(null);

  // Document upload states
  const [uploadedDocument, setUploadedDocument] = useState<{
    name: string;
    size: string;
    url?: string;
  } | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isDragOver, setIsDragOver] = useState(false);
  const { therapistData } = (useFetchTherapistData() || {
    therapistData: null,
  }) as { therapistData: TherapistData | null };

  // Handle file input change
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setProfileImage(URL.createObjectURL(file));

      const formData = new FormData();
      formData.append("upload", file);

      try {
        await uploadProfileImage(formData);

        const url = `${endpoints.setting.therapistSettingData}`;

        mutate(url, async () => {
          const fetchedData = await fetcher(url);

          // Update formik values with fallback to cached values
          const updatedValues = {
            name: cachedValues?.name || fetchedData.name,
            email: cachedValues?.email || fetchedData.email,
            phone: cachedValues?.phone || fetchedData.phone,
            address: {
              streetAddress:
                cachedValues?.address?.streetAddress ||
                fetchedData.address?.streetAddress ||
                "",
              pincode:
                cachedValues?.address?.pincode ||
                fetchedData.address?.pincode ||
                "",
              district:
                cachedValues?.address?.district ||
                fetchedData.address?.district ||
                "",
              state:
                cachedValues?.address?.state ||
                fetchedData.address?.state ||
                "",
            },
            panCard: cachedValues?.panCard || fetchedData.panCard || "",
            gstNumber: cachedValues?.gstNumber || fetchedData.gstNumber || "",
          };

          formik.setValues(updatedValues);
          setCachedValues(updatedValues);

          return fetchedData;
        });
      } catch (error) {
        console.error("Error uploading image:", error);
      }
    }
  };

  // Document upload functions
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const handleDocumentUpload = async (file: File) => {
    // Validate file type (only .docx files)
    if (!file.name.toLowerCase().endsWith(".docx")) {
      alert("Please upload only .docx files");
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert("File size should be less than 10MB");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const formData = new FormData();
      formData.append("document", file);

      // TODO: Replace with actual document upload endpoint
      // const response = await uploadDocument(formData);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Set uploaded document info
      setUploadedDocument({
        name: file.name,
        size: formatFileSize(file.size),
        url: URL.createObjectURL(file), // In real implementation, this would be the server URL
      });

      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 500);
    } catch (error) {
      console.error("Error uploading document:", error);
      setIsUploading(false);
      setUploadProgress(0);
      alert("Failed to upload document. Please try again.");
    }
  };

  const handleDocumentFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleDocumentUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleDocumentUpload(files[0]);
    }
  };

  const handleDeleteDocument = () => {
    setUploadedDocument(null);
  };

  const scrollToError = (errors: FormErrors) => {
    const firstError = Object.keys(errors)[0];
    if (!firstError) return;

    const element = document.querySelector(`[name="${firstError}"]`);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  const initialValues = useMemo(
    () => ({
      name: therapistData?.name || "",
      email: therapistData?.email || "",
      phone: therapistData?.phone || "",
      address: {
        streetAddress: therapistData?.address?.streetAddress || "",
        pincode: therapistData?.address?.pincode || "",
        district: therapistData?.address?.district || "",
        state: therapistData?.address?.state || "",
      },
      panCard: therapistData?.panCard || "",
      gstNumber: therapistData?.gstNumber || "",
    }),
    [therapistData]
  );

  // Define formik for form handling
  const formik = useFormik({
    enableReinitialize: true,
    initialValues,
    validationSchema: Yup.object({
      name: Yup.string().required("Name is required"),
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
      phone: Yup.string()
        .matches(/^\d{9,10}$/, "Phone number must be 9-10 digits")
        .required("Phone number is required"),
      address: Yup.object({
        streetAddress: Yup.string().required("Street address is required"),
        pincode: Yup.string()
          .length(6, "Pincode must be 6 digits")
          .matches(/^\d{6}$/, "Pincode must be exactly 6 digits")
          .required("Pincode is required"),
        district: Yup.string().required("District is required"),
        state: Yup.string().required("State is required"),
      }).required("Address is required"),
      panCard: Yup.string()
        .length(10, "PAN card must be 10 characters")
        .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, "Invalid PAN card format")
        .required("PAN card is required"),
      gstNumber: Yup.string().when([], {
        is: () => hasGst === "yes",
        then: (schema) =>
          schema
            .required("GST number is required")
            .length(15, "GST number must be 15 characters")
            .matches(
              /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[0-9A]{1}$/,
              "Invalid GST number format"
            ),
        otherwise: (schema) => schema.transform(() => null).nullable(),
      }),
    }),
    validateOnBlur: true,
    validateOnChange: false,
    onSubmit: async (values, { validateForm }) => {
      // Validate form and scroll to first error if any
      const errors = await validateForm(values);
      if (Object.keys(errors).length > 0) {
        scrollToError(errors);
        return;
      }

      setIsSubmitting(true);
      const payload: Omit<typeof values, "gstNumber"> & {
        gstNumber?: string | null;
      } = { ...values };

      if (hasGst !== "yes") {
        delete payload.gstNumber;
      }

      try {
        await updateSettingData(payload);
        window.location.reload();
      } catch (error) {
        console.error("Error updating settings:", error);
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  // Add this effect to handle scrolling on validation
  useEffect(() => {
    if (Object.keys(formik.errors).length > 0 && formik.submitCount > 0) {
      scrollToError(formik.errors);
    }
  }, [formik.errors, formik.submitCount]);

  // Update cached values when form fields change
  useEffect(() => {
    setCachedValues((prevValues) => ({
      ...prevValues,
      name: formik.values.name,
      email: formik.values.email,
      phone: formik.values.phone,
      address: {
        streetAddress: formik.values.address.streetAddress,
        pincode: formik.values.address.pincode,
        district: formik.values.address.district,
        state: formik.values.address.state,
      },
      panCard: formik.values.panCard,
      gstNumber: formik.values.gstNumber,
    }));
  }, [
    formik.values.name,
    formik.values.email,
    formik.values.phone,
    formik.values.address.streetAddress,
    formik.values.address.pincode,
    formik.values.address.district,
    formik.values.address.state,
    formik.values.panCard,
    formik.values.gstNumber,
  ]);

  // Update form values when therapistData is available
  useMemo(() => {
    if (therapistData) {
      // formik.setValues({
      //   name: therapistData.name || "",
      //   email: therapistData.email || "",
      //   phone: therapistData.phone || "",
      //   address: therapistData.address || "",
      // });

      setProfileImage(therapistData?.profilePhoto || null);
    }
  }, [therapistData]);

  const fetchPincodeDetails = async (pincode: string) => {
    try {
      const response = await fetch(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      const data: PincodeAPIResponse[] = await response.json();
      if (data[0]?.Status === "Success") {
        const { District, State } = data[0].PostOffice[0];
        formik.setFieldValue("address.district", District || "");
        formik.setFieldValue("address.state", State || "");
      } else {
        alert("Invalid Pincode. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching pincode details:", error);
      alert("Error fetching pincode details. Please try again.");
    }
  };

  return (
    <SettingLayout>
      <div>
        <h1 className="text-xl_30 font-semibold text-primary">
          Personal Information
        </h1>
        <form onSubmit={formik.handleSubmit}>
          <div className="grid sm:grid-cols-7 sm:gap-[62px] gap-5 pt-6">
            <div className="sm:col-span-4">
              <div className="space-y-5">
                <div>
                  <label className="text-base_18 font-medium text-primary">
                    Name
                  </label>
                  <Input
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    name="name"
                    type="text"
                    placeholder="Enter Your Name"
                    disabled={!!therapistData?.name}
                  />
                  {formik.errors.name && formik.touched.name && (
                    <p className="text-red-500 text-sm">
                      {formik.errors.name.toString()}
                    </p>
                  )}
                </div>
                <div>
                  <label className="text-base_18 font-medium text-primary">
                    Email Address
                  </label>
                  <Input
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    name="email"
                    type="email"
                    placeholder="Enter Your Email Address"
                    disabled={!!therapistData?.email}
                  />
                  {formik.errors.email && formik.touched.email && (
                    <p className="text-red-500 text-sm">
                      {formik.errors.email.toString()}
                    </p>
                  )}
                </div>
                <div>
                  <label className="text-base_18 font-medium text-primary">
                    Phone Number
                  </label>
                  <Input
                    value={formik.values.phone}
                    onChange={(e) =>
                      formik.setFieldValue("phone", e.target.value.toString())
                    }
                    name="phone"
                    type="tel"
                    placeholder="Enter Your Phone Number"
                  />
                  {formik.errors.phone && formik.touched.phone && (
                    <p className="text-red-500 text-sm">
                      {formik.errors.phone.toString()}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="sm:col-span-3 sm:order-last order-first">
              <div className="max-w-[285px] w-full bg-green-600/10 py-6 rounded-base border border-green-600/20 flex flex-col items-center gap-4">
                <div className="w-[128px] h-[128px] bg-white rounded-full border-2 border-green-600/50 border-dashed flex items-center justify-center text-green-600">
                  {/* Display the uploaded image or the UserCircle icon */}
                  {profileimage ? (
                    <Image
                      src={profileimage}
                      width={400}
                      height={400}
                      alt="Profile Preview"
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <div className="text-green-600">
                      <UserCircle size={50} weight="light" />
                    </div>
                  )}
                </div>

                <div className="text-center text-xs_18 text-primary/50">
                  <p>File types: .jpg, .png</p>
                  <p className="pt-2">Image at least: 200px*200px</p>
                </div>

                {/* File input for uploading image */}
                <input
                  type="file"
                  accept=".jpg,.png"
                  onChange={handleFileChange}
                  id="fileUpload"
                  style={{ display: "none" }}
                />

                {/* Button to trigger the file input */}
                {profileimage ? (
                  <Button
                    type="button"
                    onClick={() => {
                      const fileUpload = document.getElementById("fileUpload");
                      if (fileUpload) fileUpload.click();
                    }}
                    className="text-sm text-green-600 border border-green-600 py-2.5 px-5 bg-white rounded-full"
                  >
                    Edit Profile
                  </Button>
                ) : (
                  <Button
                    type="button"
                    onClick={() => {
                      const fileUpload = document.getElementById("fileUpload");
                      if (fileUpload) fileUpload.click();
                    }}
                    className="text-sm text-green-600 border border-green-600 py-2.5 px-5 bg-white rounded-full"
                  >
                    Upload Profile
                  </Button>
                )}
              </div>
            </div>
          </div>
          {/* <div className="flex items-center w-[200px] gap-3.5 pt-8.5">
            <Button
              variant="filled"
              className={`w-full`}
              onClick={formik.handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div> */}
        </form>
      </div>

      <div className="pt-10 lg:w-[55%]">
        <h2 className="text-xl_30 font-semibold text-primary">
          Billing Information
        </h2>
        <div className="space-y-5 pt-6">
          {/* Street Address Field */}
          <div>
            <label className="text-base_18 font-medium text-primary">
              Street Address
            </label>
            <Input
              value={formik.values.address.streetAddress}
              onChange={formik.handleChange}
              name="address.streetAddress"
              type="text"
              placeholder="Enter Your Street Address"
            />
            {formik.errors.address?.streetAddress &&
              formik.touched.address?.streetAddress && (
                <p className="text-red-500 text-sm">
                  {formik.errors.address.streetAddress}
                </p>
              )}
          </div>

          {/* Pincode Field */}
          <div>
            <label className="text-base_18 font-medium text-primary">
              Pincode
            </label>
            <Input
              value={formik.values.address.pincode}
              onChange={(e) => {
                formik.setFieldValue("address.pincode", e.target.value);
                if (e.target.value.length === 6) {
                  fetchPincodeDetails(e.target.value); // Assuming this function fetches district and state
                }
              }}
              name="address.pincode"
              type="number"
              placeholder="Enter Pincode"
            />
            {formik.errors.address?.pincode &&
              formik.touched.address?.pincode && (
                <p className="text-red-500 text-sm">
                  {formik.errors.address.pincode}
                </p>
              )}
          </div>

          {/* State and District (These will be fetched based on Pincode) */}
          <div className="flex space-x-6">
            <div className="flex-1">
              <label className="text-base_18 font-medium text-primary">
                State
              </label>
              <Input
                value={formik.values.address.state}
                onChange={formik.handleChange}
                name="address.state"
                type="text"
                placeholder="State"
                disabled
              />
            </div>
            <div className="flex-1">
              <label className="text-base_18 font-medium text-primary">
                District
              </label>
              <Input
                value={formik.values.address.district}
                onChange={formik.handleChange}
                name="address.district"
                type="text"
                placeholder="District"
                disabled
              />
            </div>
          </div>

          {/* PAN Card Field */}
          <div>
            <label className="text-base_18 font-medium text-primary">
              PAN Card
            </label>
            <Input
              value={formik.values.panCard}
              onChange={(e) => {
                const capitalized = e.target.value.toUpperCase();
                formik.setFieldValue("panCard", capitalized);
              }}
              name="panCard"
              type="text"
              placeholder="Enter Your PAN Card Number"
            />
            {formik.errors.panCard && formik.touched.panCard && (
              <p className="text-red-500 text-sm">{formik.errors.panCard}</p>
            )}
          </div>

          <div>
            <label className="text-base_18 font-medium text-primary">
              GST Number
            </label>
            {therapistData?.gstNumber ? (
              <div>
                <p className="text-primary">{therapistData.gstNumber}</p>
              </div>
            ) : (
              <div>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="hasGst"
                      value="yes"
                      onChange={() => setHasGst("yes")}
                      checked={hasGst === "yes"}
                    />
                    <span className="ml-2">Yes</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="hasGst"
                      value="no"
                      onChange={() => setHasGst("no")}
                      checked={hasGst === "no"}
                    />
                    <span className="ml-2">No</span>
                  </label>
                </div>

                {hasGst === "yes" && (
                  <div className="mt-3">
                    <Input
                      value={formik.values.gstNumber}
                      onChange={formik.handleChange}
                      name="gstNumber"
                      type="text"
                      placeholder="Enter Your GST Number"
                    />
                    {formik.errors.gstNumber && formik.touched.gstNumber && (
                      <p className="text-red-500 text-sm">
                        {formik.errors.gstNumber}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Client Consent Form Section */}
          <div className="pt-5">
            <div className="flex items-center justify-between mb-4">
              <label className="text-base_18 font-medium text-primary">
                Client Consent Form
              </label>
              <a
                href="#"
                className="text-blue-600 text-sm underline hover:text-blue-800"
                onClick={(e) => {
                  e.preventDefault();
                  // TODO: Add actual download link for sample document
                  alert("Sample document download will be implemented");
                }}
              >
                Download Sample Doc
              </a>
            </div>

            {/* Upload Area or Uploaded Document */}
            {!uploadedDocument && !isUploading ? (
              <div
                className={`border-2 border-dashed rounded-lg transition-colors min-h-[120px] flex items-center ${
                  isDragOver
                    ? "border-blue-500 bg-blue-50"
                    : "border-blue-200 bg-blue-50"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="flex items-center justify-center w-full px-6 py-4">
                  <div className="flex items-center space-x-4">
                    {/* Icon with background circle */}
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 29 28"
                        fill="none"
                      >
                        <path
                          d="M14.5009 21.0001V12.6001M14.5009 12.6001L17.1842 15.2834M14.5009 12.6001L11.8176 15.2834"
                          stroke="#2C58BB"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M16.2494 3.5L22.6661 9.91667V23.3333C22.6661 23.975 22.1411 24.5 21.4994 24.5H7.49943C6.85776 24.5 6.33276 23.975 6.33276 23.3333V4.66667C6.33276 4.025 6.85776 3.5 7.49943 3.5H16.2494Z"
                          stroke="#2C58BB"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M16.6003 3.49951V9.3333H22.6671L16.6003 3.49951Z"
                          stroke="#2C58BB"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <p className="text-gray-600 mb-1">
                        Drop your files here or{" "}
                        <button
                          type="button"
                          className="text-blue-600 underline hover:text-blue-800"
                          onClick={() => {
                            const fileInput =
                              document.getElementById("documentUpload");
                            if (fileInput) fileInput.click();
                          }}
                        >
                          Click to upload
                        </button>
                      </p>
                      <p className="text-sm text-gray-500">Supports only .docx</p>
                    </div>
                  </div>
                </div>

                <input
                  type="file"
                  accept=".docx"
                  onChange={handleDocumentFileChange}
                  id="documentUpload"
                  style={{ display: "none" }}
                />
              </div>
            ) : isUploading ? (
              // Upload Progress
              <div className="border-2 border-blue-200 rounded-lg bg-blue-50 relative min-h-[120px] flex items-center justify-center">
                <button
                  type="button"
                  className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
                  onClick={() => {
                    setIsUploading(false);
                    setUploadProgress(0);
                  }}
                >
                  <X size={20} />
                </button>

                <div className="flex flex-col items-center">
                  {/* Circular Progress */}
                  <div className="relative w-16 h-16 mb-3">
                    <svg
                      className="w-16 h-16 transform -rotate-90"
                      viewBox="0 0 100 100"
                    >
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#e5e7eb"
                        strokeWidth="8"
                        fill="none"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#3b82f6"
                        strokeWidth="8"
                        fill="none"
                        strokeLinecap="round"
                        strokeDasharray={`${2 * Math.PI * 40}`}
                        strokeDashoffset={`${
                          2 * Math.PI * 40 * (1 - uploadProgress / 100)
                        }`}
                        className="transition-all duration-300 ease-out"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-sm font-semibold text-blue-600">
                        {uploadProgress}%
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 font-medium text-sm">Uploading...</p>
                </div>
              </div>
            ) : (
              // Uploaded Document Display
              <div className="border-2 border-blue-200 rounded-lg p-4 bg-blue-50 min-h-[120px] flex items-center">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-4">
                    {/* DOC Icon */}
                    <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-white font-bold text-sm">DOC</span>
                    </div>

                    {/* File Info */}
                    <div>
                      <p className="font-medium text-gray-900 text-base">
                        {uploadedDocument?.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {uploadedDocument?.size}
                      </p>
                    </div>
                  </div>

                  {/* Action Icons */}
                  <div className="flex items-center space-x-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <button
                      type="button"
                      className="text-gray-400 hover:text-red-500 transition-colors"
                      onClick={handleDeleteDocument}
                    >
                      <Trash size={20} />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center w-[200px] gap-3.5 pt-8.5">
          <Button
            variant="filled"
            className={`w-full`}
            onClick={formik.handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Save"}
          </Button>
        </div>
      </div>
    </SettingLayout>
  );
};

export default ProfileInfo;
