"use client";
import Button from "@/components/common/Button";
import Input from "@/components/common/Input";
import { useFetchTherapistData } from "@/context/TherapistContext";
import SettingLayout from "@/layout/dashboard/SettingLayout";
import {
  updateSettingData,
  uploadProfileImage,
} from "@/services/setting.service";
import { fetcher } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import { UserCircle, File, Trash, X } from "@phosphor-icons/react";
import { useFormik } from "formik";
import Image from "next/image";
import React, { useEffect, useMemo, useState } from "react";
import { mutate } from "swr";
import * as Yup from "yup";
import { FormErrors } from "@/utils/formUtils";

interface CachedValues {
  name?: string;
  email?: string;
  phone?: string;
  address?: {
    streetAddress?: string;
    pincode?: string;
    district?: string;
    state?: string;
  };
  panCard?: string;
  gstNumber?: string;
}

interface Address {
  streetAddress?: string;
  pincode?: string;
  district?: string;
  state?: string;
}

interface TherapistData {
  name?: string;
  email?: string;
  phone?: string;
  address?: Address;
  panCard?: string;
  gstNumber?: string;
  profilePhoto?: string;
}

interface PincodeAPIResponse {
  Status: string;
  PostOffice: { District: string; State: string }[];
}

const ProfileInfo = () => {
  const [profileimage, setProfileImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cachedValues, setCachedValues] = useState<CachedValues>({});
  const [hasGst, setHasGst] = useState<null | "yes" | "no">(null);

  // Document upload states
  const [uploadedDocument, setUploadedDocument] = useState<{
    name: string;
    size: string;
    url?: string;
  } | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isDragOver, setIsDragOver] = useState(false);
  const { therapistData } = (useFetchTherapistData() || {
    therapistData: null,
  }) as { therapistData: TherapistData | null };

  // Handle file input change
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setProfileImage(URL.createObjectURL(file));

      const formData = new FormData();
      formData.append("upload", file);

      try {
        await uploadProfileImage(formData);

        const url = `${endpoints.setting.therapistSettingData}`;

        mutate(url, async () => {
          const fetchedData = await fetcher(url);

          // Update formik values with fallback to cached values
          const updatedValues = {
            name: cachedValues?.name || fetchedData.name,
            email: cachedValues?.email || fetchedData.email,
            phone: cachedValues?.phone || fetchedData.phone,
            address: {
              streetAddress:
                cachedValues?.address?.streetAddress ||
                fetchedData.address?.streetAddress ||
                "",
              pincode:
                cachedValues?.address?.pincode ||
                fetchedData.address?.pincode ||
                "",
              district:
                cachedValues?.address?.district ||
                fetchedData.address?.district ||
                "",
              state:
                cachedValues?.address?.state ||
                fetchedData.address?.state ||
                "",
            },
            panCard: cachedValues?.panCard || fetchedData.panCard || "",
            gstNumber: cachedValues?.gstNumber || fetchedData.gstNumber || "",
          };

          formik.setValues(updatedValues);
          setCachedValues(updatedValues);

          return fetchedData;
        });
      } catch (error) {
        console.error("Error uploading image:", error);
      }
    }
  };

  // Document upload functions
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const handleDocumentUpload = async (file: File) => {
    // Validate file type (only .docx files)
    if (!file.name.toLowerCase().endsWith(".docx")) {
      alert("Please upload only .docx files");
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      alert("File size should be less than 10MB");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const formData = new FormData();
      formData.append("document", file);

      // TODO: Replace with actual document upload endpoint
      // const response = await uploadDocument(formData);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Set uploaded document info
      setUploadedDocument({
        name: file.name,
        size: formatFileSize(file.size),
        url: URL.createObjectURL(file), // In real implementation, this would be the server URL
      });

      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 500);
    } catch (error) {
      console.error("Error uploading document:", error);
      setIsUploading(false);
      setUploadProgress(0);
      alert("Failed to upload document. Please try again.");
    }
  };

  const handleDocumentFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleDocumentUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleDocumentUpload(files[0]);
    }
  };

  const handleDeleteDocument = () => {
    setUploadedDocument(null);
  };

  const scrollToError = (errors: FormErrors) => {
    const firstError = Object.keys(errors)[0];
    if (!firstError) return;

    const element = document.querySelector(`[name="${firstError}"]`);
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  const initialValues = useMemo(
    () => ({
      name: therapistData?.name || "",
      email: therapistData?.email || "",
      phone: therapistData?.phone || "",
      address: {
        streetAddress: therapistData?.address?.streetAddress || "",
        pincode: therapistData?.address?.pincode || "",
        district: therapistData?.address?.district || "",
        state: therapistData?.address?.state || "",
      },
      panCard: therapistData?.panCard || "",
      gstNumber: therapistData?.gstNumber || "",
    }),
    [therapistData]
  );

  // Define formik for form handling
  const formik = useFormik({
    enableReinitialize: true,
    initialValues,
    validationSchema: Yup.object({
      name: Yup.string().required("Name is required"),
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
      phone: Yup.string()
        .matches(/^\d{9,10}$/, "Phone number must be 9-10 digits")
        .required("Phone number is required"),
      address: Yup.object({
        streetAddress: Yup.string().required("Street address is required"),
        pincode: Yup.string()
          .length(6, "Pincode must be 6 digits")
          .matches(/^\d{6}$/, "Pincode must be exactly 6 digits")
          .required("Pincode is required"),
        district: Yup.string().required("District is required"),
        state: Yup.string().required("State is required"),
      }).required("Address is required"),
      panCard: Yup.string()
        .length(10, "PAN card must be 10 characters")
        .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, "Invalid PAN card format")
        .required("PAN card is required"),
      gstNumber: Yup.string().when([], {
        is: () => hasGst === "yes",
        then: (schema) =>
          schema
            .required("GST number is required")
            .length(15, "GST number must be 15 characters")
            .matches(
              /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[A-Z0-9]{1}[Z]{1}[0-9A]{1}$/,
              "Invalid GST number format"
            ),
        otherwise: (schema) => schema.transform(() => null).nullable(),
      }),
    }),
    validateOnBlur: true,
    validateOnChange: false,
    onSubmit: async (values, { validateForm }) => {
      // Validate form and scroll to first error if any
      const errors = await validateForm(values);
      if (Object.keys(errors).length > 0) {
        scrollToError(errors);
        return;
      }

      setIsSubmitting(true);
      const payload: Omit<typeof values, "gstNumber"> & {
        gstNumber?: string | null;
      } = { ...values };

      if (hasGst !== "yes") {
        delete payload.gstNumber;
      }

      try {
        await updateSettingData(payload);
        window.location.reload();
      } catch (error) {
        console.error("Error updating settings:", error);
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  // Add this effect to handle scrolling on validation
  useEffect(() => {
    if (Object.keys(formik.errors).length > 0 && formik.submitCount > 0) {
      scrollToError(formik.errors);
    }
  }, [formik.errors, formik.submitCount]);

  // Update cached values when form fields change
  useEffect(() => {
    setCachedValues((prevValues) => ({
      ...prevValues,
      name: formik.values.name,
      email: formik.values.email,
      phone: formik.values.phone,
      address: {
        streetAddress: formik.values.address.streetAddress,
        pincode: formik.values.address.pincode,
        district: formik.values.address.district,
        state: formik.values.address.state,
      },
      panCard: formik.values.panCard,
      gstNumber: formik.values.gstNumber,
    }));
  }, [
    formik.values.name,
    formik.values.email,
    formik.values.phone,
    formik.values.address.streetAddress,
    formik.values.address.pincode,
    formik.values.address.district,
    formik.values.address.state,
    formik.values.panCard,
    formik.values.gstNumber,
  ]);

  // Update form values when therapistData is available
  useMemo(() => {
    if (therapistData) {
      // formik.setValues({
      //   name: therapistData.name || "",
      //   email: therapistData.email || "",
      //   phone: therapistData.phone || "",
      //   address: therapistData.address || "",
      // });

      setProfileImage(therapistData?.profilePhoto || null);
    }
  }, [therapistData]);

  const fetchPincodeDetails = async (pincode: string) => {
    try {
      const response = await fetch(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      const data: PincodeAPIResponse[] = await response.json();
      if (data[0]?.Status === "Success") {
        const { District, State } = data[0].PostOffice[0];
        formik.setFieldValue("address.district", District || "");
        formik.setFieldValue("address.state", State || "");
      } else {
        alert("Invalid Pincode. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching pincode details:", error);
      alert("Error fetching pincode details. Please try again.");
    }
  };

  return (
    <SettingLayout>
      <div>
        <h1 className="text-xl_30 font-semibold text-primary">
          Personal Information
        </h1>
        <form onSubmit={formik.handleSubmit}>
          <div className="grid sm:grid-cols-7 sm:gap-[62px] gap-5 pt-6">
            <div className="sm:col-span-4">
              <div className="space-y-5">
                <div>
                  <label className="text-base_18 font-medium text-primary">
                    Name
                  </label>
                  <Input
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    name="name"
                    type="text"
                    placeholder="Enter Your Name"
                    disabled={!!therapistData?.name}
                  />
                  {formik.errors.name && formik.touched.name && (
                    <p className="text-red-500 text-sm">
                      {formik.errors.name.toString()}
                    </p>
                  )}
                </div>
                <div>
                  <label className="text-base_18 font-medium text-primary">
                    Email Address
                  </label>
                  <Input
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    name="email"
                    type="email"
                    placeholder="Enter Your Email Address"
                    disabled={!!therapistData?.email}
                  />
                  {formik.errors.email && formik.touched.email && (
                    <p className="text-red-500 text-sm">
                      {formik.errors.email.toString()}
                    </p>
                  )}
                </div>
                <div>
                  <label className="text-base_18 font-medium text-primary">
                    Phone Number
                  </label>
                  <Input
                    value={formik.values.phone}
                    onChange={(e) =>
                      formik.setFieldValue("phone", e.target.value.toString())
                    }
                    name="phone"
                    type="tel"
                    placeholder="Enter Your Phone Number"
                  />
                  {formik.errors.phone && formik.touched.phone && (
                    <p className="text-red-500 text-sm">
                      {formik.errors.phone.toString()}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="sm:col-span-3 sm:order-last order-first">
              <div className="max-w-[285px] w-full bg-green-600/10 py-6 rounded-base border border-green-600/20 flex flex-col items-center gap-4">
                <div className="w-[128px] h-[128px] bg-white rounded-full border-2 border-green-600/50 border-dashed flex items-center justify-center text-green-600">
                  {/* Display the uploaded image or the UserCircle icon */}
                  {profileimage ? (
                    <Image
                      src={profileimage}
                      width={400}
                      height={400}
                      alt="Profile Preview"
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <div className="text-green-600">
                      <UserCircle size={50} weight="light" />
                    </div>
                  )}
                </div>

                <div className="text-center text-xs_18 text-primary/50">
                  <p>File types: .jpg, .png</p>
                  <p className="pt-2">Image at least: 200px*200px</p>
                </div>

                {/* File input for uploading image */}
                <input
                  type="file"
                  accept=".jpg,.png"
                  onChange={handleFileChange}
                  id="fileUpload"
                  style={{ display: "none" }}
                />

                {/* Button to trigger the file input */}
                {profileimage ? (
                  <Button
                    type="button"
                    onClick={() => {
                      const fileUpload = document.getElementById("fileUpload");
                      if (fileUpload) fileUpload.click();
                    }}
                    className="text-sm text-green-600 border border-green-600 py-2.5 px-5 bg-white rounded-full"
                  >
                    Edit Profile
                  </Button>
                ) : (
                  <Button
                    type="button"
                    onClick={() => {
                      const fileUpload = document.getElementById("fileUpload");
                      if (fileUpload) fileUpload.click();
                    }}
                    className="text-sm text-green-600 border border-green-600 py-2.5 px-5 bg-white rounded-full"
                  >
                    Upload Profile
                  </Button>
                )}
              </div>
            </div>
          </div>
          {/* <div className="flex items-center w-[200px] gap-3.5 pt-8.5">
            <Button
              variant="filled"
              className={`w-full`}
              onClick={formik.handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div> */}
        </form>
      </div>

      <div className="pt-10 lg:w-[55%]">
        <h2 className="text-xl_30 font-semibold text-primary">
          Billing Information
        </h2>
        <div className="space-y-5 pt-6">
          {/* Street Address Field */}
          <div>
            <label className="text-base_18 font-medium text-primary">
              Street Address
            </label>
            <Input
              value={formik.values.address.streetAddress}
              onChange={formik.handleChange}
              name="address.streetAddress"
              type="text"
              placeholder="Enter Your Street Address"
            />
            {formik.errors.address?.streetAddress &&
              formik.touched.address?.streetAddress && (
                <p className="text-red-500 text-sm">
                  {formik.errors.address.streetAddress}
                </p>
              )}
          </div>

          {/* Pincode Field */}
          <div>
            <label className="text-base_18 font-medium text-primary">
              Pincode
            </label>
            <Input
              value={formik.values.address.pincode}
              onChange={(e) => {
                formik.setFieldValue("address.pincode", e.target.value);
                if (e.target.value.length === 6) {
                  fetchPincodeDetails(e.target.value); // Assuming this function fetches district and state
                }
              }}
              name="address.pincode"
              type="number"
              placeholder="Enter Pincode"
            />
            {formik.errors.address?.pincode &&
              formik.touched.address?.pincode && (
                <p className="text-red-500 text-sm">
                  {formik.errors.address.pincode}
                </p>
              )}
          </div>

          {/* State and District (These will be fetched based on Pincode) */}
          <div className="flex space-x-6">
            <div className="flex-1">
              <label className="text-base_18 font-medium text-primary">
                State
              </label>
              <Input
                value={formik.values.address.state}
                onChange={formik.handleChange}
                name="address.state"
                type="text"
                placeholder="State"
                disabled
              />
            </div>
            <div className="flex-1">
              <label className="text-base_18 font-medium text-primary">
                District
              </label>
              <Input
                value={formik.values.address.district}
                onChange={formik.handleChange}
                name="address.district"
                type="text"
                placeholder="District"
                disabled
              />
            </div>
          </div>

          {/* PAN Card Field */}
          <div>
            <label className="text-base_18 font-medium text-primary">
              PAN Card
            </label>
            <Input
              value={formik.values.panCard}
              onChange={(e) => {
                const capitalized = e.target.value.toUpperCase();
                formik.setFieldValue("panCard", capitalized);
              }}
              name="panCard"
              type="text"
              placeholder="Enter Your PAN Card Number"
            />
            {formik.errors.panCard && formik.touched.panCard && (
              <p className="text-red-500 text-sm">{formik.errors.panCard}</p>
            )}
          </div>

          <div>
            <label className="text-base_18 font-medium text-primary">
              GST Number
            </label>
            {therapistData?.gstNumber ? (
              <div>
                <p className="text-primary">{therapistData.gstNumber}</p>
              </div>
            ) : (
              <div>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="hasGst"
                      value="yes"
                      onChange={() => setHasGst("yes")}
                      checked={hasGst === "yes"}
                    />
                    <span className="ml-2">Yes</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="hasGst"
                      value="no"
                      onChange={() => setHasGst("no")}
                      checked={hasGst === "no"}
                    />
                    <span className="ml-2">No</span>
                  </label>
                </div>

                {hasGst === "yes" && (
                  <div className="mt-3">
                    <Input
                      value={formik.values.gstNumber}
                      onChange={formik.handleChange}
                      name="gstNumber"
                      type="text"
                      placeholder="Enter Your GST Number"
                    />
                    {formik.errors.gstNumber && formik.touched.gstNumber && (
                      <p className="text-red-500 text-sm">
                        {formik.errors.gstNumber}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Client Consent Form Section */}
          <div className="pt-5">
            <div className="flex items-center justify-between mb-4">
              <label className="text-base_18 font-medium text-primary">
                Client Consent Form
              </label>
              <a
                href="#"
                className="text-blue-600 text-sm underline hover:text-blue-800"
                onClick={(e) => {
                  e.preventDefault();
                  // TODO: Add actual download link for sample document
                  alert("Sample document download will be implemented");
                }}
              >
                Download Sample Doc
              </a>
            </div>

            {/* Upload Area or Uploaded Document */}
            {!uploadedDocument && !isUploading ? (
              <div
                className={`border-2 border-dashed rounded-lg p-6 transition-colors ${
                  isDragOver
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-300 bg-gray-50"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <div className="flex items-center space-x-4">
                  {/* Icon with background circle */}
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 29 28"
                      fill="none"
                    >
                      <path
                        d="M14.5009 21.0001V12.6001M14.5009 12.6001L17.1842 15.2834M14.5009 12.6001L11.8176 15.2834"
                        stroke="#2C58BB"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M16.2494 3.5L22.6661 9.91667V23.3333C22.6661 23.975 22.1411 24.5 21.4994 24.5H7.49943C6.85776 24.5 6.33276 23.975 6.33276 23.3333V4.66667C6.33276 4.025 6.85776 3.5 7.49943 3.5H16.2494Z"
                        stroke="#2C58BB"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M16.6003 3.49951V9.3333H22.6671L16.6003 3.49951Z"
                        stroke="#2C58BB"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>

                  {/* Content */}
                  <div className="flex-1">
                    <p className="text-gray-600 mb-1">
                      Drop your files here or{" "}
                      <button
                        type="button"
                        className="text-blue-600 underline hover:text-blue-800"
                        onClick={() => {
                          const fileInput =
                            document.getElementById("documentUpload");
                          if (fileInput) fileInput.click();
                        }}
                      >
                        Click to upload
                      </button>
                    </p>
                    <p className="text-sm text-gray-500">Supports only .docx</p>
                  </div>
                </div>

                <input
                  type="file"
                  accept=".docx"
                  onChange={handleDocumentFileChange}
                  id="documentUpload"
                  style={{ display: "none" }}
                />
              </div>
            ) : isUploading ? (
              // Upload Progress
              <div className="border-2 border-gray-300 rounded-lg p-8 text-center bg-white relative">
                <button
                  type="button"
                  className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
                  onClick={() => {
                    setIsUploading(false);
                    setUploadProgress(0);
                  }}
                >
                  <X size={20} />
                </button>

                <div className="flex flex-col items-center">
                  {/* Circular Progress */}
                  <div className="relative w-20 h-20 mb-4">
                    <svg
                      className="w-20 h-20 transform -rotate-90"
                      viewBox="0 0 100 100"
                    >
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#e5e7eb"
                        strokeWidth="8"
                        fill="none"
                      />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        stroke="#3b82f6"
                        strokeWidth="8"
                        fill="none"
                        strokeLinecap="round"
                        strokeDasharray={`${2 * Math.PI * 40}`}
                        strokeDashoffset={`${
                          2 * Math.PI * 40 * (1 - uploadProgress / 100)
                        }`}
                        className="transition-all duration-300 ease-out"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-lg font-semibold text-blue-600">
                        {uploadProgress}%
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-600 font-medium">Uploading...</p>
                </div>
              </div>
            ) : (
              // Uploaded Document Display
              <div className="border border-gray-300 rounded-lg p-4 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="40"
                      height="40"
                      viewBox="0 0 40 40"
                      fill="none"
                    >
                      <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M9.69462 0H24L36 12V34.7926C36 37.6711 33.5088 40 30.4403 40H9.57031C6.49114 40 4 37.6711 4 34.7926V5.20733C3.99995 2.32878 6.61539 0 9.69462 0Z"
                        fill="#2C58BB"
                      />
                      <path
                        d="M24.0257 8.16V0L36 12H28.0171C24.4248 12 23.8593 9.44 24.0257 8.16Z"
                        fill="white"
                        fill-opacity="0.3"
                      />
                      <path
                        d="M12.7115 32H10.3912V25.4545H12.7306C13.389 25.4545 13.9558 25.5856 14.4309 25.8477C14.9061 26.1076 15.2715 26.4815 15.5272 26.9695C15.785 27.4574 15.9139 28.0412 15.9139 28.7209C15.9139 29.4027 15.785 29.9886 15.5272 30.4787C15.2715 30.9687 14.9039 31.3448 14.4245 31.6069C13.9473 31.869 13.3762 32 12.7115 32ZM11.775 30.8143H12.6539C13.063 30.8143 13.4071 30.7418 13.6863 30.5969C13.9675 30.4499 14.1784 30.223 14.3191 29.9162C14.4618 29.6072 14.5332 29.2088 14.5332 28.7209C14.5332 28.2372 14.4618 27.842 14.3191 27.5352C14.1784 27.2283 13.9686 27.0025 13.6895 26.8576C13.4103 26.7127 13.0662 26.6403 12.6571 26.6403H11.775V30.8143ZM22.9539 28.7273C22.9539 29.4411 22.8186 30.0483 22.548 30.549C22.2796 31.0497 21.9131 31.4322 21.4486 31.6964C20.9862 31.9585 20.4664 32.0895 19.8889 32.0895C19.3073 32.0895 18.7852 31.9574 18.3229 31.6932C17.8605 31.429 17.4951 31.0465 17.2267 30.5458C16.9582 30.0451 16.824 29.4389 16.824 28.7273C16.824 28.0135 16.9582 27.4062 17.2267 26.9055C17.4951 26.4048 17.8605 26.0234 18.3229 25.7614C18.7852 25.4972 19.3073 25.3651 19.8889 25.3651C20.4664 25.3651 20.9862 25.4972 21.4486 25.7614C21.9131 26.0234 22.2796 26.4048 22.548 26.9055C22.8186 27.4062 22.9539 28.0135 22.9539 28.7273ZM21.5509 28.7273C21.5509 28.2649 21.4816 27.875 21.3431 27.5575C21.2068 27.2401 21.0139 26.9993 20.7646 26.8352C20.5154 26.6712 20.2235 26.5891 19.8889 26.5891C19.5544 26.5891 19.2625 26.6712 19.0132 26.8352C18.7639 26.9993 18.57 27.2401 18.4316 27.5575C18.2952 27.875 18.227 28.2649 18.227 28.7273C18.227 29.1896 18.2952 29.5795 18.4316 29.897C18.57 30.2145 18.7639 30.4553 19.0132 30.6193C19.2625 30.7834 19.5544 30.8654 19.8889 30.8654C20.2235 30.8654 20.5154 30.7834 20.7646 30.6193C21.0139 30.4553 21.2068 30.2145 21.3431 29.897C21.4816 29.5795 21.5509 29.1896 21.5509 28.7273ZM29.7543 27.7461H28.3544C28.3288 27.565 28.2766 27.4041 28.1978 27.2635C28.119 27.1207 28.0178 26.9993 27.8942 26.8991C27.7706 26.799 27.6278 26.7223 27.4659 26.669C27.3061 26.6158 27.1325 26.5891 26.945 26.5891C26.6062 26.5891 26.3111 26.6733 26.0597 26.8416C25.8082 27.0078 25.6133 27.2507 25.4748 27.5703C25.3363 27.8878 25.267 28.2734 25.267 28.7273C25.267 29.1939 25.3363 29.5859 25.4748 29.9034C25.6154 30.2209 25.8114 30.4606 26.0629 30.6225C26.3143 30.7844 26.6051 30.8654 26.9354 30.8654C27.1207 30.8654 27.2923 30.8409 27.4499 30.7919C27.6097 30.7429 27.7514 30.6715 27.875 30.5778C27.9986 30.4819 28.1009 30.3658 28.1818 30.2294C28.2649 30.093 28.3224 29.9375 28.3544 29.7628L29.7543 29.7692C29.718 30.0696 29.6275 30.3594 29.4826 30.6385C29.3398 30.9155 29.147 31.1637 28.9041 31.3832C28.6634 31.6005 28.3757 31.7731 28.0412 31.9009C27.7088 32.0266 27.3327 32.0895 26.913 32.0895C26.3292 32.0895 25.8072 31.9574 25.3469 31.6932C24.8888 31.429 24.5266 31.0465 24.2603 30.5458C23.9961 30.0451 23.864 29.4389 23.864 28.7273C23.864 28.0135 23.9982 27.4062 24.2667 26.9055C24.5352 26.4048 24.8995 26.0234 25.3597 25.7614C25.82 25.4972 26.3377 25.3651 26.913 25.3651C27.2923 25.3651 27.6438 25.4183 27.9677 25.5249C28.2937 25.6314 28.5824 25.7869 28.8338 25.9915C29.0852 26.1939 29.2898 26.4421 29.4474 26.7362C29.6072 27.0302 29.7095 27.3668 29.7543 27.7461Z"
                        fill="white"
                      />
                    </svg>
                    <div>
                      <p className="font-medium text-gray-900">
                        {uploadedDocument?.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {uploadedDocument?.size}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <button
                      type="button"
                      className="text-gray-400 hover:text-red-500 transition-colors"
                      onClick={handleDeleteDocument}
                    >
                      <Trash size={20} />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="flex items-center w-[200px] gap-3.5 pt-8.5">
          <Button
            variant="filled"
            className={`w-full`}
            onClick={formik.handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Save"}
          </Button>
        </div>
      </div>
    </SettingLayout>
  );
};

export default ProfileInfo;
